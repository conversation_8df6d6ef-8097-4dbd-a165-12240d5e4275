package entities

// Food 食物实体 - 包含业务逻辑和数据库映射
type Food struct {
	ID         int     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	FoodName   string  `gorm:"column:food_name;type:varchar(255)" json:"foodName"`
	Measure    string  `gorm:"column:measure;type:varchar(100)" json:"measure"`
	Grams      float64 `gorm:"column:grams;type:double" json:"grams"`
	Calories   float64 `gorm:"column:calories;type:double" json:"calories"`
	Protein    float64 `gorm:"column:protein;type:double" json:"protein"`
	Fat        float64 `gorm:"column:fat;type:double" json:"fat"`
	SatFat     float64 `gorm:"column:sat_fat;type:double" json:"satFat"`
	Fiber      string  `gorm:"column:fiber;type:varchar(20)" json:"fiber"`
	Carbs      float64 `gorm:"column:carbs;type:double" json:"carbs"`
	ImageURL   string  `gorm:"column:image_url;type:varchar(255)" json:"imageUrl,omitempty"`
	CategoryID int     `gorm:"column:category_id;type:int" json:"categoryId"`
}

// TableName 指定表名
func (Food) TableName() string {
	return "food"
}

// NewFood 创建新食物实体
func NewFood(foodName, measure string, categoryID int) *Food {
	return &Food{
		FoodName:   foodName,
		Measure:    measure,
		CategoryID: categoryID,
		Grams:      0,
		Calories:   0,
		Protein:    0,
		Fat:        0,
		SatFat:     0,
		Fiber:      "",
		Carbs:      0,
	}
}

// IsValidNutrition 检查营养成分数据是否合理
func (f *Food) IsValidNutrition() bool {
	// 基本合理性检查：营养成分不能为负数
	return f.Grams >= 0 && f.Calories >= 0 && f.Protein >= 0 && 
		   f.Fat >= 0 && f.SatFat >= 0 && f.Carbs >= 0
}

// HasImage 检查是否有图片
func (f *Food) HasImage() bool {
	return f.ImageURL != ""
}

// SetImageURL 设置食物图片URL
func (f *Food) SetImageURL(url string) {
	f.ImageURL = url
}

// GetNutritionSummary 获取营养成分摘要
func (f *Food) GetNutritionSummary() map[string]float64 {
	return map[string]float64{
		"calories": f.Calories,
		"protein":  f.Protein,
		"fat":      f.Fat,
		"carbs":    f.Carbs,
	}
}

// UpdateNutrition 更新营养成分信息
func (f *Food) UpdateNutrition(grams, calories, protein, fat, satFat, carbs float64, fiber string) {
	f.Grams = grams
	f.Calories = calories
	f.Protein = protein
	f.Fat = fat
	f.SatFat = satFat
	f.Fiber = fiber
	f.Carbs = carbs
}
